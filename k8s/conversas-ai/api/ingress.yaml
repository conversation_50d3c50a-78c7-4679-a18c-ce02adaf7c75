apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: conversas-ai-api-ingress
  namespace: conversas-ai
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
spec:
  ingressClassName: nginx
  rules:
  - host: api.conversas.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: conversas-ai-api-service
            port:
              number: 8080
