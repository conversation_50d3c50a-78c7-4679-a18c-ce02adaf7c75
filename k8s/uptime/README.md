# 📊 Uptime Kuma - Monitoramento de Uptime

Este diretório contém a configuração completa para deploy do **Uptime Kuma** no Kubernetes, equivalente ao comando Docker:

```bash
docker run -d --restart=always -p 3001:3001 -v uptime-kuma:/app/data --name uptime-kuma louislam/uptime-kuma:1
```

## 📋 Índice

- [🚀 Instalação Rápida](#-instalação-rápida)
- [📦 Componentes](#-componentes)
- [🔧 Instalação Detalhada](#-instalação-detalhada)
- [🌐 Acesso](#-acesso)
- [🛠️ Troubleshooting](#️-troubleshooting)

## 🚀 Instalação Rápida

```bash
cd k8s/uptime
chmod +x deploy.sh
./deploy.sh
```

Escolha a opção **1** para deploy completo.

## 📦 Componentes

### Arquivos de Configuração:
- `namespace.yaml` - Namespace dedicado para o Uptime Kuma
- `pvc.yaml` - Armazenamento persistente para dados (5GB)
- `deployment.yaml` - Deployment principal do Uptime Kuma
- `service.yaml` - Service interno do Kubernetes
- `ingress.yaml` - Exposição externa via NGINX Ingress
- `deploy.sh` - Script automatizado de instalação

### Especificações:
- **Imagem**: `louislam/uptime-kuma:1`
- **Porta**: `3001`
- **Armazenamento**: `5GB` (PersistentVolume)
- **Recursos**: 
  - CPU: 100m (request) / 500m (limit)
  - Memory: 256Mi (request) / 512Mi (limit)

## 🔧 Instalação Detalhada

### Passo 1: Criar Namespace
```bash
kubectl apply -f namespace.yaml
```

### Passo 2: Criar Armazenamento Persistente
```bash
kubectl apply -f pvc.yaml
```

### Passo 3: Deploy da Aplicação
```bash
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml
kubectl apply -f ingress.yaml
```

### Passo 4: Verificar Status
```bash
kubectl get pods -n uptime
kubectl wait --for=condition=available --timeout=300s deployment/uptime-kuma -n uptime
```

## 🌐 Acesso

### URL de Acesso:
- **Produção**: https://uptime.conversas.ai

### Configuração Inicial:
1. Acesse a URL do Uptime Kuma
2. Configure usuário administrador na primeira execução
3. Adicione seus serviços para monitoramento

## 🛠️ Troubleshooting

### Verificar Logs:
```bash
kubectl logs -f deployment/uptime-kuma -n uptime
```

### Verificar Status dos Pods:
```bash
kubectl get pods -n uptime
kubectl describe pod <pod-name> -n uptime
```

### Verificar PVC:
```bash
kubectl get pvc -n uptime
kubectl describe pvc uptime-kuma-data -n uptime
```

### Verificar Ingress:
```bash
kubectl get ingress -n uptime
kubectl describe ingress uptime-kuma-ingress -n uptime
```

### Problemas Comuns:

#### 1. Pod não inicia:
```bash
# Verificar eventos
kubectl get events -n uptime --sort-by='.lastTimestamp'

# Verificar recursos
kubectl describe pod <pod-name> -n uptime
```

#### 2. Não consegue acessar via URL:
```bash
# Verificar ingress
kubectl get ingress -n uptime


#### 3. Dados não persistem:
```bash
kubectl get pvc -n uptime
kubectl describe pvc uptime-kuma-data -n uptime
```

## 🔄 Operações de Manutenção

### Reiniciar Aplicação:
```bash
kubectl rollout restart deployment/uptime-kuma -n uptime
```

### Backup dos Dados:
```bash
# Os dados estão no PVC uptime-kuma-data
# Para backup, você pode usar ferramentas como Velero ou scripts customizados
```

### Atualizar Versão:
```bash
# Editar deployment.yaml e alterar a tag da imagem
kubectl set image deployment/uptime-kuma uptime-kuma=louislam/uptime-kuma:latest -n uptime
```

### Desinstalar:
```bash
./deploy.sh  # Escolha opção 2
```

## 📞 Suporte

- **Uptime Kuma**: https://github.com/louislam/uptime-kuma
- **Documentação**: https://github.com/louislam/uptime-kuma/wiki

---

## 🔗 Estrutura de Arquivos

```
k8s/uptime/
├── 📋 README.md          # Esta documentação
├── 🚀 deploy.sh          # Script de instalação automatizada
├── 🏗️ namespace.yaml     # Namespace uptime
├── 💾 pvc.yaml           # Armazenamento persistente
├── 🐳 deployment.yaml    # Deployment do Uptime Kuma
├── 🌐 service.yaml       # Service interno
└── 🔗 ingress.yaml       # Exposição externa
```

## ✅ Equivalência Docker → Kubernetes

| Docker | Kubernetes |
|--------|------------|
| `--restart=always` | `restartPolicy: Always` |
| `-p 3001:3001` | `Service` + `Ingress` |
| `-v uptime-kuma:/app/data` | `PersistentVolumeClaim` |
| `--name uptime-kuma` | `metadata.name` |
| `louislam/uptime-kuma:1` | `image: louislam/uptime-kuma:1` |
