#!/bin/bash

# PostgreSQL Deployment Script
# This script deploys PostgreSQL using CrunchyData postgres-operator

set -e

echo "🚀 Starting PostgreSQL deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if postgres-operator is installed
print_status "Checking if CrunchyData postgres-operator is installed..."
if ! kubectl get crd postgresclusters.postgres-operator.crunchydata.com &> /dev/null; then
    print_warning "CrunchyData postgres-operator not found. Installing..."
    
    # Install postgres-operator
    kubectl apply --server-side -k https://github.com/CrunchyData/postgres-operator/config/default
    
    print_status "Waiting for postgres-operator to be ready..."
    kubectl wait --for=condition=Available deployment/pgo -n postgres-operator --timeout=300s
    
    print_success "CrunchyData postgres-operator installed successfully"
else
    print_success "CrunchyData postgres-operator is already installed"
fi

# Deploy PostgreSQL components
print_status "Creating namespace..."
kubectl apply -f namespace.yaml

print_status "Creating secrets..."
kubectl apply -f secrets.yaml

print_status "Creating PVCs..."
kubectl apply -f pvc.yaml

print_status "Creating services..."
kubectl apply -f service.yaml

print_status "Creating PostgreSQL cluster..."
kubectl apply -f postgres-cluster.yaml

print_status "Creating ingress..."
kubectl apply -f ingress.yaml

# Wait for PostgreSQL cluster to be ready
print_status "Waiting for PostgreSQL cluster to be ready..."
kubectl wait --for=condition=PostgresClusterInitialized postgrescluster/postgres-cluster -n postgres --timeout=600s

# Check cluster status
print_status "Checking cluster status..."
kubectl get postgrescluster -n postgres

# Get connection information
print_status "Getting connection information..."
echo ""
echo "📋 PostgreSQL Connection Information:"
echo "======================================"
echo "Namespace: postgres"
echo "Service: postgres-service.postgres.svc.cluster.local:5432"
echo "Database: conversas_ai"
echo "User: conversas_ai_user"
echo ""
echo "🔐 To get the password:"
echo "kubectl get secret postgres-cluster-pguser-conversas-ai-user -n postgres -o jsonpath='{.data.password}' | base64 -d"
echo ""
echo "🌐 External Access (Cloudflare Zero Trust):"
echo "URL: https://pg.conversas.ai"
echo "Protection: Cloudflare Access (<EMAIL>)"
echo ""

# Show pods status
print_status "PostgreSQL pods status:"
kubectl get pods -n postgres -l postgres-operator.crunchydata.com/cluster=postgres-cluster

print_success "PostgreSQL deployment completed successfully!"

echo ""
echo "🔧 Next Steps:"
echo "1. Configure Cloudflare Zero Trust tunnel for pg.conversas.ai"
echo "2. Set up database schemas and initial data"
echo "3. Configure application connection strings"
echo "4. Set up monitoring and alerting"
echo "5. Configure backup schedules"

echo ""
print_warning "Remember to:"
echo "- Keep the CREDENTIALS.md file secure and do not commit it to Git"
echo "- Set up proper RBAC for database access"
echo "- Configure network policies if needed"
echo "- Monitor database performance and storage usage"
