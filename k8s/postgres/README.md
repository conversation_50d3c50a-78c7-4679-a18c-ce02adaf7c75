# 🐘 PostgreSQL Deployment - CrunchyData Operator

Este diretório contém os arquivos necessários para deploy do PostgreSQL usando o CrunchyData postgres-operator no Kubernetes.

## 📁 Arquivos

- `namespace.yaml` - Namespace do PostgreSQL
- `secrets.yaml` - <PERSON><PERSON><PERSON><PERSON><PERSON> seguras (senhas base64)
- `postgres-cluster.yaml` - Configuração do cluster PostgreSQL
- `service.yaml` - Services para acesso interno
- `ingress.yaml` - Ingress com Cloudflare Zero Trust
- `pvc.yaml` - Persistent Volume Claims
- `CREDENTIALS.md` - Credenciais em texto claro (NÃO commitar!)

## 🚀 Deploy

### 1. Instalar CrunchyData Postgres Operator

```bash
# Instalar o operator
kubectl apply --server-side -k https://github.com/CrunchyData/postgres-operator/config/default

# Aguardar operator ficar pronto
kubectl wait --for=condition=Available deployment/pgo -n postgres-operator --timeout=300s
```

### 2. Deploy dos Componentes

```bash
# Aplicar na ordem
kubectl apply -f namespace.yaml
kubectl apply -f secrets.yaml
kubectl apply -f pvc.yaml
kubectl apply -f service.yaml
kubectl apply -f postgres-cluster.yaml
kubectl apply -f ingress.yaml
```

### 3. Verificar Status

```bash
# Aguardar cluster ficar pronto
kubectl wait --for=condition=PostgresClusterInitialized postgrescluster/postgres-cluster -n postgres --timeout=600s

# Verificar status
kubectl get postgrescluster -n postgres
kubectl get pods -n postgres
```

## 🔐 Acesso

### Interno (Cluster)
```
Host: postgres-service.postgres.svc.cluster.local
Port: 5432
Database: conversas_ai
User: conversas_ai_user
```

### Externo (Cloudflare Zero Trust)
```
URL: https://pg.conversas.ai
Proteção: Cloudflare Access (<EMAIL>)
Port: 5432
```

### Obter Senha
```bash
kubectl get secret postgres-cluster-pguser-conversas-ai-user -n postgres -o jsonpath='{.data.password}' | base64 -d
```

## ⚙️ Configuração

### Recursos
- **CPU**: 500m request, 2000m limit
- **Memory**: 1Gi request, 4Gi limit  
- **Storage**: 20Gi data + 20Gi backup + 10Gi WAL

### PostgreSQL Settings
- max_connections: 200
- shared_buffers: 256MB
- effective_cache_size: 1GB
- Backup retention: 14 dias

## 🔧 Manutenção

### Backup Manual
```bash
kubectl exec -it postgres-cluster-instance1-xxxx -n postgres -- pg_dump conversas_ai > backup.sql
```

### Escalar Replicas
```bash
kubectl patch postgrescluster postgres-cluster -n postgres --type='merge' -p='{"spec":{"instances":[{"name":"instance1","replicas":2}]}}'
```

### Atualizar Senhas
```bash
# Gerar nova senha
openssl rand -base64 32

# Atualizar secret
kubectl patch secret postgres-secrets -n postgres -p='{"data":{"APP_PASSWORD":"NOVA_SENHA_BASE64"}}'

# Reiniciar cluster
kubectl rollout restart deployment/postgres-cluster-instance1 -n postgres
```

## 🌐 Cloudflare Zero Trust

### Configuração Necessária
1. Criar tunnel no Cloudflare Dashboard
2. Configurar DNS: `pg.conversas.ai` → tunnel
3. Configurar Access Policy:
   - Email: `<EMAIL>`
   - Action: Allow

### Teste de Conectividade
```bash
# Via psql (após autenticação Cloudflare)
psql "postgresql://conversas_ai_user:<EMAIL>:5432/conversas_ai"
```

## 📊 Monitoramento

### Métricas Disponíveis
- PostgreSQL Exporter integrado
- Métricas expostas na porta 9187
- Compatible com Prometheus/Grafana

### Logs
```bash
# Logs do PostgreSQL
kubectl logs -f postgres-cluster-instance1-xxxx -n postgres

# Logs do operator
kubectl logs -f deployment/pgo -n postgres-operator
```

## 🚨 Troubleshooting

### Cluster não inicia
```bash
# Verificar events
kubectl describe postgrescluster postgres-cluster -n postgres

# Verificar logs do operator
kubectl logs deployment/pgo -n postgres-operator
```

### Problemas de conectividade
```bash
# Testar service interno
kubectl run test-pod --rm -i --tty --image=postgres:15 -- bash
psql "postgresql://conversas_ai_user:<EMAIL>:5432/conversas_ai"
```

### Storage issues
```bash
# Verificar PVCs
kubectl get pvc -n postgres

# Verificar storage usage
kubectl exec postgres-cluster-instance1-xxxx -n postgres -- df -h
```

## ⚠️ Segurança

- ✅ Senhas com 256 bits de entropia
- ✅ Armazenamento em Kubernetes Secrets
- ✅ Cloudflare Zero Trust protection
- ✅ Network policies (se configuradas)
- ✅ RBAC restrito ao namespace

## 📞 Suporte

Em caso de problemas:
1. Verificar logs do operator e cluster
2. Consultar documentação CrunchyData
3. Verificar configuração Cloudflare Zero Trust
