apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: postgres-ingress
  namespace: postgres
  labels:
    app: postgres
  annotations:
    # Cloudflare specific annotations
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/backend-protocol: "TCP"
    nginx.ingress.kubernetes.io/tcp-services-configmap: "postgres/postgres-tcp-services"
    # Security annotations
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    # Cloudflare Zero Trust protection
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "CF-Access-Authenticated-User-Email $http_cf_access_authenticated_user_email";
      more_set_headers "CF-Access-JWT-Assertion $http_cf_access_jwt_assertion";
    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit: "10"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
    - hosts:
        - pg.conversas.ai
      secretName: postgres-tls-secret
  rules:
    - host: pg.conversas.ai
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: postgres-service
                port:
                  number: 5432
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-tcp-services
  namespace: postgres
  labels:
    app: postgres
data:
  5432: "postgres/postgres-service:5432"
---
# TLS Secret placeholder - should be managed by cert-manager or Cloudflare
apiVersion: v1
kind: Secret
metadata:
  name: postgres-tls-secret
  namespace: postgres
  labels:
    app: postgres
type: kubernetes.io/tls
data:
  # These should be replaced with actual certificates
  # For Cloudflare Zero Trust, certificates are managed by Cloudflare
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t  # placeholder
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t  # placeholder
