apiVersion: postgres-operator.crunchydata.com/v1beta1
kind: PostgresCluster
metadata:
  name: postgres-cluster
  namespace: postgres
  labels:
    app: postgres
spec:
  image: registry.developers.crunchydata.com/crunchydata/crunchy-postgres:ubi8-15.4-1
  postgresVersion: 15
  
  # Instance configuration
  instances:
    - name: instance1
      replicas: 1
      resources:
        requests:
          cpu: "500m"
          memory: "1Gi"
        limits:
          cpu: "2000m"
          memory: "4Gi"
      dataVolumeClaimSpec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: 20Gi
        storageClassName: standard-rwo
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              podAffinityTerm:
                topologyKey: kubernetes.io/hostname
                labelSelector:
                  matchLabels:
                    postgres-operator.crunchydata.com/cluster: postgres-cluster
                    postgres-operator.crunchydata.com/instance-set: instance1

  # Backup configuration
  backups:
    pgbackrest:
      image: registry.developers.crunchydata.com/crunchydata/crunchy-pgbackrest:ubi8-2.47-1
      repos:
        - name: repo1
          volume:
            volumeClaimSpec:
              accessModes:
                - "ReadWriteOnce"
              resources:
                requests:
                  storage: 20Gi
              storageClassName: standard-rwo
      configuration:
        - secret:
            name: postgres-secrets
      global:
        repo1-retention-full: "14"
        repo1-retention-full-type: time

  # User and database configuration
  users:
    - name: conversas_ai_user
      databases:
        - conversas_ai
      options: "CREATEDB"
      password:
        type: AlphaNumeric

  # PostgreSQL configuration
  patroni:
    dynamicConfiguration:
      postgresql:
        parameters:
          max_connections: "200"
          shared_buffers: "256MB"
          effective_cache_size: "1GB"
          maintenance_work_mem: "64MB"
          checkpoint_completion_target: "0.9"
          wal_buffers: "16MB"
          default_statistics_target: "100"
          random_page_cost: "1.1"
          effective_io_concurrency: "200"
          work_mem: "4MB"
          min_wal_size: "1GB"
          max_wal_size: "4GB"
          max_worker_processes: "8"
          max_parallel_workers_per_gather: "2"
          max_parallel_workers: "8"
          max_parallel_maintenance_workers: "2"

  # Monitoring
  monitoring:
    pgmonitor:
      exporter:
        image: registry.developers.crunchydata.com/crunchydata/crunchy-postgres-exporter:ubi8-5.3.1-0
