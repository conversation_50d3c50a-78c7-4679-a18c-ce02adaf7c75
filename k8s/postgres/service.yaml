apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: postgres
  labels:
    app: postgres
spec:
  type: ClusterIP
  ports:
    - name: postgres
      port: 5432
      targetPort: 5432
      protocol: TCP
  selector:
    postgres-operator.crunchydata.com/cluster: postgres-cluster
    postgres-operator.crunchydata.com/role: master
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-primary
  namespace: postgres
  labels:
    app: postgres
    service-type: primary
spec:
  type: ClusterIP
  ports:
    - name: postgres
      port: 5432
      targetPort: 5432
      protocol: TCP
  selector:
    postgres-operator.crunchydata.com/cluster: postgres-cluster
    postgres-operator.crunchydata.com/role: master
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-replica
  namespace: postgres
  labels:
    app: postgres
    service-type: replica
spec:
  type: ClusterIP
  ports:
    - name: postgres
      port: 5432
      targetPort: 5432
      protocol: TCP
  selector:
    postgres-operator.crunchydata.com/cluster: postgres-cluster
    postgres-operator.crunchydata.com/role: replica
